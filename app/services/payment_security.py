"""
支付安全机制服务
"""
import hashlib
import hmac
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.database import SessionLocal
from app.models.payment import Order, PaymentRecord, PaymentLog, OrderStatus
from app.models import User

logger = logging.getLogger(__name__)


class PaymentSecurityService:
    """支付安全服务"""
    
    def __init__(self):
        # 安全配置
        self.max_order_per_user_per_hour = 10  # 每小时最大订单数
        self.max_payment_attempts_per_order = 5  # 每个订单最大支付尝试次数
        self.suspicious_ip_threshold = 20  # 可疑IP阈值（每小时订单数）
        self.order_timeout_minutes = 30  # 订单超时时间（分钟）
        self.payment_retry_interval_seconds = 60  # 支付重试间隔（秒）
    
    def validate_order_creation(
        self, 
        db: Session, 
        user_id: int, 
        client_ip: Optional[str] = None
    ) -> Tuple[bool, str]:
        """
        验证订单创建的安全性
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            client_ip: 客户端IP
            
        Returns:
            Tuple[bool, str]: (是否通过验证, 错误消息)
        """
        try:
            # 1. 检查用户是否存在
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False, "用户不存在"
            
            # 2. 检查用户订单频率
            if not self._check_user_order_frequency(db, user_id):
                return False, "订单创建过于频繁，请稍后再试"
            
            # 3. 检查IP频率（如果提供了IP）
            if client_ip and not self._check_ip_frequency(db, client_ip):
                return False, "该IP地址订单创建过于频繁"
            
            # 4. 检查用户是否有过多未支付订单
            if not self._check_pending_orders(db, user_id):
                return False, "您有过多未支付订单，请先完成或取消现有订单"
            
            return True, "验证通过"
            
        except Exception as e:
            logger.error(f"订单创建安全验证异常: {e}")
            return False, "安全验证失败"
    
    def _check_user_order_frequency(self, db: Session, user_id: int) -> bool:
        """检查用户订单创建频率"""
        try:
            # 检查最近1小时的订单数
            one_hour_ago = datetime.now() - timedelta(hours=1)
            
            order_count = db.query(func.count(Order.id)).filter(
                and_(
                    Order.user_id == user_id,
                    Order.created_at >= one_hour_ago
                )
            ).scalar() or 0
            
            return order_count < self.max_order_per_user_per_hour
            
        except Exception as e:
            logger.error(f"检查用户订单频率异常: {e}")
            return False
    
    def _check_ip_frequency(self, db: Session, client_ip: str) -> bool:
        """检查IP订单创建频率"""
        try:
            # 检查最近1小时该IP的订单数
            one_hour_ago = datetime.now() - timedelta(hours=1)
            
            order_count = db.query(func.count(Order.id)).filter(
                and_(
                    Order.client_ip == client_ip,
                    Order.created_at >= one_hour_ago
                )
            ).scalar() or 0
            
            return order_count < self.suspicious_ip_threshold
            
        except Exception as e:
            logger.error(f"检查IP频率异常: {e}")
            return False
    
    def _check_pending_orders(self, db: Session, user_id: int) -> bool:
        """检查用户未支付订单数量"""
        try:
            pending_count = db.query(func.count(Order.id)).filter(
                and_(
                    Order.user_id == user_id,
                    Order.status == OrderStatus.PENDING
                )
            ).scalar() or 0
            
            # 限制每个用户最多有3个未支付订单
            return pending_count < 3
            
        except Exception as e:
            logger.error(f"检查未支付订单异常: {e}")
            return False
    
    def validate_payment_request(
        self, 
        db: Session, 
        order_id: str, 
        user_id: int
    ) -> Tuple[bool, str]:
        """
        验证支付请求的安全性
        
        Args:
            db: 数据库会话
            order_id: 订单ID
            user_id: 用户ID
            
        Returns:
            Tuple[bool, str]: (是否通过验证, 错误消息)
        """
        try:
            # 1. 检查订单是否存在且属于该用户
            order = db.query(Order).filter(
                and_(
                    Order.id == order_id,
                    Order.user_id == user_id
                )
            ).first()
            
            if not order:
                return False, "订单不存在或无权限访问"
            
            # 2. 检查订单状态
            if order.status != OrderStatus.PENDING:
                return False, f"订单状态不允许支付: {order.status.value}"
            
            # 3. 检查订单是否过期
            if datetime.now() > order.expired_at:
                return False, "订单已过期"
            
            # 4. 检查支付尝试次数
            if not self._check_payment_attempts(db, order_id):
                return False, "支付尝试次数过多，请稍后再试"
            
            # 5. 检查支付重试间隔
            if not self._check_payment_retry_interval(db, order_id):
                return False, "支付请求过于频繁，请稍后再试"
            
            return True, "验证通过"
            
        except Exception as e:
            logger.error(f"支付请求安全验证异常: {e}")
            return False, "安全验证失败"
    
    def _check_payment_attempts(self, db: Session, order_id: str) -> bool:
        """检查支付尝试次数"""
        try:
            # 统计该订单的支付尝试次数
            attempt_count = db.query(func.count(PaymentLog.id)).filter(
                and_(
                    PaymentLog.order_id == order_id,
                    PaymentLog.action == "create_wechat_payment"
                )
            ).scalar() or 0
            
            return attempt_count < self.max_payment_attempts_per_order
            
        except Exception as e:
            logger.error(f"检查支付尝试次数异常: {e}")
            return False
    
    def _check_payment_retry_interval(self, db: Session, order_id: str) -> bool:
        """检查支付重试间隔"""
        try:
            # 获取最近一次支付尝试时间
            last_attempt = db.query(PaymentLog.created_at).filter(
                and_(
                    PaymentLog.order_id == order_id,
                    PaymentLog.action == "create_wechat_payment"
                )
            ).order_by(PaymentLog.created_at.desc()).first()
            
            if not last_attempt:
                return True  # 没有之前的尝试记录
            
            # 检查是否超过重试间隔
            time_diff = datetime.now() - last_attempt[0]
            return time_diff.total_seconds() >= self.payment_retry_interval_seconds
            
        except Exception as e:
            logger.error(f"检查支付重试间隔异常: {e}")
            return False
    
    def detect_duplicate_payment(
        self, 
        db: Session, 
        callback_data: Dict[str, Any]
    ) -> bool:
        """
        检测重复支付
        
        Args:
            db: 数据库会话
            callback_data: 回调数据
            
        Returns:
            bool: 是否为重复支付
        """
        try:
            transaction_id = callback_data.get("transaction_id")
            if not transaction_id:
                return False
            
            # 检查是否已有相同交易号的支付记录
            existing_payment = db.query(PaymentRecord).filter(
                PaymentRecord.wx_transaction_id == transaction_id
            ).first()
            
            return existing_payment is not None
            
        except Exception as e:
            logger.error(f"检测重复支付异常: {e}")
            return False
    
    def validate_callback_signature(
        self, 
        headers: Dict[str, str], 
        body: str,
        api_key: str
    ) -> bool:
        """
        验证回调签名（简化版本）
        
        Args:
            headers: 请求头
            body: 请求体
            api_key: API密钥
            
        Returns:
            bool: 签名是否有效
        """
        try:
            # 获取签名相关信息
            timestamp = headers.get('Wechatpay-Timestamp', '')
            nonce = headers.get('Wechatpay-Nonce', '')
            signature = headers.get('Wechatpay-Signature', '')
            
            if not all([timestamp, nonce, signature]):
                logger.error("回调签名验证失败: 缺少必要的头部信息")
                return False
            
            # 检查时间戳是否在合理范围内（5分钟内）
            try:
                callback_time = datetime.fromtimestamp(int(timestamp))
                time_diff = abs((datetime.now() - callback_time).total_seconds())
                if time_diff > 300:  # 5分钟
                    logger.error(f"回调时间戳过期: {time_diff}秒")
                    return False
            except (ValueError, OSError):
                logger.error("无效的时间戳格式")
                return False
            
            # 这里应该实现真正的微信支付签名验证
            # 由于需要微信的平台证书，这里简化处理
            logger.info("回调签名验证通过（简化版本）")
            return True
            
        except Exception as e:
            logger.error(f"验证回调签名异常: {e}")
            return False
    
    def generate_order_hash(self, order_data: Dict[str, Any]) -> str:
        """
        生成订单哈希值，用于防篡改
        
        Args:
            order_data: 订单数据
            
        Returns:
            str: 订单哈希值
        """
        try:
            # 提取关键字段
            key_fields = [
                str(order_data.get("user_id", "")),
                str(order_data.get("plan_id", "")),
                str(order_data.get("amount", "")),
                str(order_data.get("created_at", ""))
            ]
            
            # 生成哈希
            hash_input = "|".join(key_fields)
            return hashlib.sha256(hash_input.encode()).hexdigest()
            
        except Exception as e:
            logger.error(f"生成订单哈希异常: {e}")
            return ""
    
    def log_security_event(
        self, 
        db: Session, 
        event_type: str, 
        user_id: Optional[int] = None,
        order_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        记录安全事件
        
        Args:
            db: 数据库会话
            event_type: 事件类型
            user_id: 用户ID
            order_id: 订单ID
            ip_address: IP地址
            details: 事件详情
        """
        try:
            log = PaymentLog(
                order_id=order_id,
                user_id=user_id,
                action=f"security_{event_type}",
                status="logged",
                request_data=details or {},
                ip_address=ip_address
            )
            
            db.add(log)
            db.commit()
            
            logger.warning(f"安全事件记录: {event_type}, 用户: {user_id}, IP: {ip_address}")
            
        except Exception as e:
            logger.error(f"记录安全事件异常: {e}")
    
    def get_security_statistics(self, db: Session, hours: int = 24) -> Dict[str, Any]:
        """
        获取安全统计信息
        
        Args:
            db: 数据库会话
            hours: 统计时间范围（小时）
            
        Returns:
            Dict[str, Any]: 安全统计信息
        """
        try:
            start_time = datetime.now() - timedelta(hours=hours)
            
            # 统计各类安全事件
            security_events = db.query(
                PaymentLog.action,
                func.count(PaymentLog.id).label('count')
            ).filter(
                and_(
                    PaymentLog.action.like('security_%'),
                    PaymentLog.created_at >= start_time
                )
            ).group_by(PaymentLog.action).all()
            
            # 统计可疑IP
            suspicious_ips = db.query(
                Order.client_ip,
                func.count(Order.id).label('order_count')
            ).filter(
                and_(
                    Order.created_at >= start_time,
                    Order.client_ip.isnot(None)
                )
            ).group_by(Order.client_ip).having(
                func.count(Order.id) >= self.suspicious_ip_threshold
            ).all()
            
            # 统计失败的支付尝试
            failed_payments = db.query(func.count(PaymentLog.id)).filter(
                and_(
                    PaymentLog.action == "create_wechat_payment",
                    PaymentLog.status == "failed",
                    PaymentLog.created_at >= start_time
                )
            ).scalar() or 0
            
            return {
                "period_hours": hours,
                "start_time": start_time,
                "security_events": {event.action: event.count for event in security_events},
                "suspicious_ips": [{"ip": ip.client_ip, "order_count": ip.order_count} for ip in suspicious_ips],
                "failed_payment_attempts": failed_payments,
                "total_security_events": sum(event.count for event in security_events)
            }
            
        except Exception as e:
            logger.error(f"获取安全统计异常: {e}")
            return {
                "period_hours": hours,
                "start_time": start_time,
                "security_events": {},
                "suspicious_ips": [],
                "failed_payment_attempts": 0,
                "total_security_events": 0
            }


# 创建全局实例
payment_security_service = PaymentSecurityService()
