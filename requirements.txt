fastapi>=0.115.0
uvicorn>=0.24.0
pydantic>=2.8.0
jinja2>=3.1.2
python-multipart>=0.0.6
httpx>=0.24.0
# 数据库相关依赖
sqlalchemy>=2.0.0
pymysql>=1.0.0
cryptography>=3.4.8
# JWT认证相关
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
# 微信API相关
requests>=2.28.0
# 环境变量管理
python-dotenv>=1.0.0
# 图片处理
Pillow>=10.0.0
# Excel文件处理
pandas>=2.0.0
openpyxl>=3.1.0
fastapi>=0.115.0
uvicorn>=0.24.0
pydantic>=2.8.0
jinja2>=3.1.2
python-multipart>=0.0.6
httpx>=0.24.0
# 数据库相关依赖
sqlalchemy>=2.0.0
pymysql>=1.0.0
cryptography>=3.4.8
# JWT认证相关
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
# 微信API相关
requests>=2.28.0
# 环境变量管理
python-dotenv>=1.0.0
# 图片处理
Pillow>=10.0.0
# Excel文件处理
pandas>=2.0.0
openpyxl>=3.1.0